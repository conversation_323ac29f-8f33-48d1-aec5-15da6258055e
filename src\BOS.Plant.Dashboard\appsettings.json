{"ConnectionStrings": {"DefaultConnection": "Data Source=BOSPlantSystem.db"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.SignalR": "Debug", "BOS.Plant.Dashboard.Services": "Debug", "BOS.Plant.Modules.OpcDa": "Debug"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.SignalR": "Debug", "BOS.Plant.Dashboard.Services": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/blazor-dashboard-.log", "rollingInterval": "Day", "retainedFileCountLimit": 10, "fileSizeLimitBytes": 10485760, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}]}, "EmailSettings": {"Enabled": false, "SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "EnableSsl": true, "UserName": "", "Password": "", "FromEmail": "<EMAIL>", "FromName": "<PERSON><PERSON>", "ReplyToEmail": "<EMAIL>", "TimeoutSeconds": 30, "BaseUrl": "https://localhost:7129"}, "OpcService": {"HubUrl": "http://localhost:5231/hubs/opcdata"}, "AllowedHosts": "*"}