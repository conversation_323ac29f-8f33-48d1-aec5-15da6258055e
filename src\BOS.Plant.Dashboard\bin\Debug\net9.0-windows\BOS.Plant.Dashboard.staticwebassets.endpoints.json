{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js", "AssetFile": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000669792364"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"p/TXD92fxVFbGsbb+dOA3rXexGDZOIe0KByLSYuBZ4w=\""}, {"Name": "ETag", "Value": "W/\"5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 14:21:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg="}]}, {"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js", "AssetFile": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6859"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Feb 2025 14:29:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg="}]}, {"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js.gz", "AssetFile": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1492"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"p/TXD92fxVFbGsbb+dOA3rXexGDZOIe0KByLSYuBZ4w=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 14:21:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p/TXD92fxVFbGsbb+dOA3rXexGDZOIe0KByLSYuBZ4w="}]}, {"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.lp4d2hvui5.js", "AssetFile": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6859"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Feb 2025 14:29:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lp4d2hvui5"}, {"Name": "integrity", "Value": "sha256-5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg="}, {"Name": "label", "Value": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.b8x8f7e52z.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "73366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b8x8f7e52z"}, {"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015358624"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "65109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0=\""}, {"Name": "ETag", "Value": "W/\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 14:21:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "606059"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css.gz", "AssetFile": "_content/MudBlazor/MudBlazor.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "65109"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 14:21:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KbJW+rhjqdyrTVVpb8Xu8Ix9cV/SmwVkUOesJFPr5w0="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000064616180"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15475"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE=\""}, {"Name": "ETag", "Value": "W/\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 14:21:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "73366"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O2UJyVCl+4RaA/jZS/WQ4tMRIZbpXtfRhr5eVl0xLm8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js.gz", "AssetFile": "_content/MudBlazor/MudBlazor.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15475"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 14:21:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.sowobu9fea.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "606059"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Jul 2025 21:24:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sowobu9fea"}, {"Name": "integrity", "Value": "sha256-fC5+7WJvRi9RO0JSMRTe5Fw/ybv1XWp6LgTNpuRA4vM="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css"}]}, {"Route": "background.png", "AssetFile": "background.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "888155"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"X3frRIqS/yX4OTi1rXbEHnvejK1+yJoa1V3OgVNDKPA=\""}, {"Name": "Last-Modified", "Value": "Fri, 04 Jul 2025 22:52:31 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X3frRIqS/yX4OTi1rXbEHnvejK1+yJoa1V3OgVNDKPA="}]}, {"Route": "background.pqa5jg1248.png", "AssetFile": "background.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "888155"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"X3frRIqS/yX4OTi1rXbEHnvejK1+yJoa1V3OgVNDKPA=\""}, {"Name": "Last-Modified", "Value": "Fri, 04 Jul 2025 22:52:31 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pqa5jg1248"}, {"Name": "integrity", "Value": "sha256-X3frRIqS/yX4OTi1rXbEHnvejK1+yJoa1V3OgVNDKPA="}, {"Name": "label", "Value": "background.png"}]}, {"Route": "favicon.2jeq8efc6q.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000342348511"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2920"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=\""}, {"Name": "ETag", "Value": "W/\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 14:21:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2jeq8efc6q"}, {"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.2jeq8efc6q.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}, {"Name": "Last-Modified", "Value": "Thu, 26 Jun 2025 12:23:32 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2jeq8efc6q"}, {"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.2jeq8efc6q.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2920"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 14:21:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2jeq8efc6q"}, {"Name": "integrity", "Value": "sha256-2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA="}, {"Name": "label", "Value": "favicon.ico.gz"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000342348511"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2920"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=\""}, {"Name": "ETag", "Value": "W/\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 14:21:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}, {"Name": "Last-Modified", "Value": "Thu, 26 Jun 2025 12:23:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}]}, {"Route": "favicon.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2920"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 14:21:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2QmB0roWK84FU9Ju+PK0EsKm/9obifm2L/+HWs2VtiA="}]}]}